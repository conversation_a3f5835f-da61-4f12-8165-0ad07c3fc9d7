"""
Basic API tests for Interview Scheduling Microservice.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_health_check():
    """Test the main health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_api_v1_health_check():
    """Test the API v1 health check endpoint."""
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
    assert response.json()["version"] == "v1"


def test_plivo_health_check():
    """Test the Plivo health check endpoint."""
    response = client.get("/plivo/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"


def test_start_scheduling_endpoint_structure():
    """Test that the start-scheduling endpoint exists and has proper structure."""
    # This test will fail without proper database connection, but verifies endpoint exists
    response = client.post("/api/v1/start-scheduling", json={
        "candidates": [],
        "round_info": "technical",
        "company_name": "Test Company",
        "job_id": "test_job",
        "panelists": []
    })
    
    # We expect either success or a database connection error
    # The important thing is that the endpoint exists and processes the request
    assert response.status_code in [202, 500]  # Success or internal server error


def test_plivo_webhook_endpoint_structure():
    """Test that the Plivo webhook endpoint exists and has proper structure."""
    # This test will fail without proper database connection, but verifies endpoint exists
    response = client.post("/plivo/status-callback", json={
        "CallUUID": "test-uuid",
        "From": "+1234567890",
        "To": "+0987654321",
        "CallStatus": "completed"
    })
    
    # We expect either success or a database connection error
    # The important thing is that the endpoint exists and processes the request
    assert response.status_code in [200, 500]  # Success or internal server error
