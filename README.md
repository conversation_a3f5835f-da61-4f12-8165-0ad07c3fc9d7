# Interview Scheduling Microservice

A FastAPI-based microservice for automating interview scheduling between candidates and panelists using Plivo voice calls.

## Features

- **Automated Interview Scheduling**: Uses Plivo Agent Flow API to make automated calls to candidates
- **MongoDB Integration**: Stores candidate, panelist, and interview data with proper indexing
- **Celery Background Tasks**: Handles scheduling and retry logic asynchronously
- **RESTful API**: Clean API endpoints for starting scheduling and handling webhooks
- **Comprehensive Validation**: Pydantic models with proper type hints and validation
- **Error Handling**: Robust error handling with proper logging and status tracking

## Architecture

The microservice follows a clean architecture pattern with:

- **API Layer**: FastAPI endpoints for external communication
- **Service Layer**: Business logic for scheduling, webhooks, and Plivo integration
- **Data Layer**: MongoDB models with Motor async driver
- **Task Layer**: Celery tasks for background processing

## Quick Start

### Prerequisites

- Python 3.8+
- MongoDB
- Redis
- Plivo Account (for production)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd interview-scheduling-microservice
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start MongoDB and Redis:
```bash
docker-compose up -d
```

5. Run the application:
```bash
python run.py
```

The API will be available at `http://localhost:8000`

### API Documentation

Once running, visit:
- API Documentation: `http://localhost:8000/docs`
- Alternative Documentation: `http://localhost:8000/redoc`

## API Endpoints

### POST /api/v1/start-scheduling

Initializes the interview scheduling process with candidate and panelist data.

**Request Body:**
```json
{
  "candidates": [
    {
      "candidate_id": "cand_001",
      "name": "John Doe",
      "phone_number": "+**********",
      "email": "<EMAIL>"
    }
  ],
  "round_info": "technical",
  "company_name": "Tech Corp",
  "job_id": "job_001",
  "panelists": [
    {
      "panelist_id": "panel_001",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_001",
          "date": "2025-08-01",
          "start_time": "10:00 AM",
          "end_time": "11:00 AM",
          "timezone": "Asia/Kolkata"
        }
      ]
    }
  ]
}
```

### POST /plivo/status-callback

Webhook endpoint for Plivo to send call status updates and DTMF input.

## Data Models

### CandidateStatus
Tracks the current state of each candidate in the scheduling process.

### Panelist
Stores panelist information and their available time slots.

### Interview
Records successfully scheduled interviews with all details.

## Configuration

Key environment variables:

- `MONGO_URI`: MongoDB connection string
- `REDIS_HOST`, `REDIS_PORT`: Redis configuration for Celery
- `PLIVO_ACCOUNT_ID`, `PLIVO_INTERVIEW_FLOW_ID`: Plivo API credentials
- `PLIVO_WEBHOOK_URL`: Base URL for webhook callbacks

## Development

### Running Tests

```bash
pytest tests/
```

### Code Style

The project follows PEP 8 and uses:
- Type hints throughout
- Comprehensive docstrings
- Async/await patterns
- Proper error handling

### Database Migrations

The application automatically creates necessary indexes on startup.

## Deployment

### Using Docker

```bash
docker-compose up --build
```

### Production Considerations

- Set up proper MongoDB replica set
- Configure Redis for high availability
- Set up proper logging and monitoring
- Configure Plivo webhooks with HTTPS
- Set up proper authentication and authorization

## Monitoring

The service includes:
- Health check endpoints (`/health`, `/api/v1/health`, `/plivo/health`)
- Comprehensive logging
- Database connection monitoring

## Contributing

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation as needed
4. Ensure all type hints and docstrings are present

## License

[Add your license information here]