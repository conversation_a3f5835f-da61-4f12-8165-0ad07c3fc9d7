# Product Requirements Document (PRD) for Interview Scheduling Microservice (MVP)

Here's the updated PRD, reflecting the NLP-based slot selection and the revised payloads to and from Plivo.

## 1. Introduction

This document outlines the requirements for the Interview Scheduling Microservice (ISM), a standalone Python/FastAPI application. The primary goal of this MVP is to automate the process of scheduling interviews between eligible candidates and available panelists using automated phone calls via Plivo, managing all related data within its own MongoDB instance. This service will initially operate independently, with future plans for integration with the main Veda platform.

## 2. Goals

- Automate interview scheduling for candidates.
- Reduce manual effort for recruiters in coordinating interview times.
- Provide a self-service option for candidates to select interview slots via natural language interaction within an interactive voice response (IVR) system.
- Track the status of interview scheduling attempts and scheduled interviews.

## 3. Out of Scope (for MVP)

- Direct integration with the Veda platform (no API calls from Veda to ISM, no webhooks from ISM to Veda).
- Skill-based matching for panelists (any panelist can interview any candidate for now).
- Complex retry logic for calls beyond a fixed number of attempts and delay.
- Candidate-initiated rescheduling of booked interviews.
- Panelist-initiated changes to booked interviews (e.g., reassigning due to unavailability).
- Google Meet link generation or email reminders to candidates/panelists (handled externally).
- Advanced analytics or reporting dashboards.
- Multi-tenancy.

## 4. Key Concepts & Definitions

- **Candidate**: An individual eligible for an interview, identified by candidate_id, name, phone_number, email.
- **Panelist**: An individual who conducts interviews, identified by panelist_id, name, email, and available_slots.
- **Available Slot**: A specific time window when a panelist is available for an interview.
- **Interview Round**: Defines the type of interview (e.g., "technical", "HR").
- **Plivo Agent Flow API**: Plivo's service for building and executing predefined call workflows, now leveraging natural language processing (NLP) for user input.
- **Celery**: Python-based asynchronous task queue for managing call attempts and retries.
- **Redis**: Message broker for Celery.
- **MongoDB**: Primary data store for the microservice.

## 5. Data Models (MongoDB Schema)

### 5.1. CandidateStatus Collection

Stores the current state of a candidate within the interview scheduling process.

- `_id`: ObjectId (Primary Key)
- `candidate_id`: String (Unique identifier for the candidate)
- `name`: String
- `phone_number`: String (Required, E.164 format, e.g., "+************")
- `email`: String
- `job_id`: String
- `round_info`: String (e.g., "technical", "HR")
- `company_name`: String
- `status`: Enum String (PENDING_CALL, CALL_INITIATED, INTERVIEW_SCHEDULED, RETRY_REQUIRED, NOT_INTERESTED, MAX_RETRIES_EXCEEDED)
- `interview_attempts`: Integer (Default: 0, increments on each call attempt)
- `last_attempted_at`: DateTime (Timestamp of the last call attempt)
- `scheduled_interview_id`: ObjectId (References Interview record, if scheduled)
- `created_at`: DateTime
- `updated_at`: DateTime

### 5.2. Panelist Collection

Stores panelist details and their availability.

- `_id`: ObjectId (Primary Key)
- `panelist_id`: String (Unique identifier for the panelist)
- `name`: String
- `email`: String
- `available_slots`: Array of Slot objects
  - `slot_id`: String (Unique ID for this specific slot)
  - `date`: Date (YYYY-MM-DD)
  - `start_time`: Time (HH:MM AM/PM)
  - `end_time`: Time (HH:MM AM/PM)
  - `timezone`: String (e.g., "Asia/Kolkata")
  - `is_booked`: Boolean (Default: false)
  - `booked_candidate_id`: String (References CandidateStatus.candidate_id if booked)
  - `booked_interview_id`: ObjectId (References Interview._id if booked)
- `created_at`: DateTime
- `updated_at`: DateTime

### 5.3. Interview Collection

Stores details of a successfully scheduled interview.

- `_id`: ObjectId (Primary Key)
- `candidate_id`: String (References CandidateStatus.candidate_id)
- `panelist_ids`: Array of Strings (References Panelist.panelist_id for all panelists involved)
- `job_id`: String
- `round_info`: String
- `company_name`: String
- `scheduled_time`: Object
  - `date`: Date (YYYY-MM-DD)
  - `start_time`: Time (HH:MM AM/PM)
  - `end_time`: Time (HH:MM AM/PM)
  - `timezone`: String
- `status`: Enum String (SCHEDULED, CANCELLED, COMPLETED, NO_SHOW)
- `created_at`: DateTime
- `updated_at`: DateTime

## 6. Functional Requirements

### 6.1. System Initialization / Seed Data

The microservice will accept an initial payload containing a list of Candidate objects, along with global round_info, company_name, job_id, and a list of Panelist objects with their available_slots.

This data will be used to populate the CandidateStatus and Panelist collections in the local MongoDB.

**Initial Payload Structure (for POST /api/v1/start-scheduling):**

```json
{
  "candidates": [
    {
      "candidate_id": "cand_001",
      "name": "Arjun Sharma",
      "phone_number": "+************",
      "email": "<EMAIL>"
    },
    {
      "candidate_id": "cand_002",
      "name": "Priya Singh",
      "phone_number": "+918765432109",
      "email": "<EMAIL>"
    },
    {
      "candidate_id": "cand_003",
      "name": "Rahul Kumar",
      "phone_number": "+917654321098",
      "email": "<EMAIL>"
    }
  ],
  "round_info": "technical",
  "company_name": "Innovate Solutions Pvt. Ltd.",
  "job_id": "job_SWE_001",
  "panelists": [
    {
      "panelist_id": "panel_T1",
      "name": "Dr. Kavita Rao",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_T1_0801_10AM",
          "date": "2025-08-01",
          "start_time": "10:00 AM",
          "end_time": "11:00 AM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T1_0801_03PM",
          "date": "2025-08-01",
          "start_time": "03:00 PM",
          "end_time": "04:00 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T1_0802_11AM",
          "date": "2025-08-02",
          "start_time": "11:00 AM",
          "end_time": "12:00 PM",
          "timezone": "Asia/Kolkata"
        }
      ]
    },
    {
      "panelist_id": "panel_T2",
      "name": "Mr. Vikram Mehta",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_T2_0801_11AM",
          "date": "2025-08-01",
          "start_time": "11:00 AM",
          "end_time": "12:00 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_T2_0802_09AM",
          "date": "2025-08-02",
          "start_time": "09:00 AM",
          "end_time": "10:00 AM",
          "timezone": "Asia/Kolkata"
        }
      ]
    },
    {
      "panelist_id": "panel_HR1",
      "name": "Ms. Anjali Gupta",
      "email": "<EMAIL>",
      "available_slots": [
        {
          "slot_id": "slot_HR1_0801_02PM",
          "date": "2025-08-01",
          "start_time": "02:00 PM",
          "end_time": "02:45 PM",
          "timezone": "Asia/Kolkata"
        },
        {
          "slot_id": "slot_HR1_0802_10AM",
          "date": "2025-08-02",
          "start_time": "10:00 AM",
          "end_time": "10:45 AM",
          "timezone": "Asia/Kolkata"
        }
      ]
    }
  ]
}
```

### 6.2. Interview Scheduling Process

**Job Queue (Celery):**
- The microservice will maintain an internal Celery queue for scheduling_tasks.
- Each scheduling_task will correspond to a CandidateStatus record.
- When a candidate is ready for a call, a scheduling_task will be added to the queue. Initially, all candidates from the seeded data will be added.

**Worker Logic:**
- A Celery worker will fetch a scheduling_task from the queue.
- It will retrieve the CandidateStatus record from its MongoDB.
- The system will keep track of the "currently selected panelist". After an interview is successfully scheduled with this panelist, the system moves to the next panelist in a defined order (e.g., alphabetical by panelist_id). If an interview is not scheduled (retry, not interested), the system attempts the next candidate with the same panelist (if they still have available slots) to fill up that panelist's schedule first, before moving to the next panelist only if the current one has no more slots.

**Panelist and Slot Selection:**
- For the current candidate, it will pick all the available slots from the currently selected panelist.
- An "available slot" is one where is_booked is false.
- If the currently selected panelist has no available slots left, the worker will move to the next panelist in the sorted list of panelists.
- If no panelist has available slots for the candidate, the candidate's status will be updated to MAX_RETRIES_EXCEEDED and the task will complete.

**Plivo Call Initiation (via Agent Flow API):**
- If a suitable panelist and available slots are found, the worker will prepare the callData payload for Plivo's Agent Flow API.
- The callData will include:
  - `to`: The candidate's phone number (formatted to E.164).
  - `webhook_endpoint`: The URL of the microservice's Plivo webhook endpoint (e.g., http://your_public_ip_or_ngrok_url:8000/plivo/status-callback).
  - `variables`: A JSON object containing dynamic data for the Plivo Agent Flow. This will include:
    - `candidate_id`: The ID of the candidate being called (crucial for webhook callback).
    - `candidate_name`: Candidate's name.
    - `company_name`: Company name.
    - `round_info`: Interview round type.
    - `job_id`: Job ID.
    - `panelist_id`: ID of the selected panelist.
    - `available_slots_details`: A list of dictionaries, each containing the full details of available slots. This is vital for Plivo's NLP to understand and match natural language responses.

```json
[
  {
    "slot_id": "slot_T1_0801_10AM",
    "date": "2025-08-01",
    "start_time": "10:00 AM",
    "end_time": "11:00 AM",
    "timezone": "Asia/Kolkata",
    "display_text": "August 1st, 10 AM to 11 AM IST"
  },
  {
    "slot_id": "slot_T1_0801_03PM",
    "date": "2025-08-01",
    "start_time": "03:00 PM",
    "end_time": "04:00 PM",
    "timezone": "Asia/Kolkata",
    "display_text": "August 1st, 3 PM to 4 PM IST"
  }
]
```

- The worker will make an HTTP POST request to `https://agentflow.plivo.com/v1/account/{PLIVO_ACCOUNT_ID}/flow/{PLIVO_INTERVIEW_FLOW_ID}`.
- Update CandidateStatus.status to CALL_INITIATED and last_attempted_at.

### 6.3. Plivo Webhook Handling (NLP-Based Slot Selection)

The microservice will expose a webhook endpoint (POST /plivo/status-callback) for Plivo to send call status updates and extracted natural language information.

This webhook will receive a JSON payload from Plivo.

**Expected JSON Request Body from Plivo:**

```json
{
  "CallUUID": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx", // Unique ID for the call
  "From": "+**********", // Plivo's outgoing number
  "To": "+************", // Candidate's phone number
  "CallStatus": "completed", // e.g., "completed", "no-answer", "busy", "failed"
  "Direction": "outbound",
  "DialStatus": "completed", // e.g., "completed", "failed", "busy", "no-answer"
  "HangupCause": "NORMAL_CLEARING", // Reason for call termination (e.g., "NO_ANSWER", "BUSY", "NORMAL_CLEARING", "USER_BUSY")
  "Duration": "60", // Call duration in seconds

  // --- Crucial Custom Data Extracted by Plivo Agent Flow's NLP ---
  "CustomData": {
    "candidate_id": "cand_001", // The candidate_id we passed to Plivo
    "panelist_id": "pan_001", // The panelist_id we passed to Plivo
    "selected_slot": "2025-08-01T10:00:00+05:30", // The extracted slot in ISO 8601 format (if chosen)
    "outcome_reason": "scheduled", // NLP-derived outcome: "scheduled", "busy", "not_interested", "no_valid_response"
    "raw_user_input": "I'd like the 10 AM slot on August 1st" // Optional: Raw transcript of user's response
  },
  // Note: 'Digits' field is NOT expected for slot selection with NLP.
  // Call 'status' (CallStatus) and 'candidate_id' (from CustomData) are guaranteed.
}
```

**Processing:**
- Parse the incoming JSON payload.
- Extract To (candidate's phone number), CallStatus, HangupCause, and the CustomData dictionary, specifically candidate_id, selected_slot, outcome_reason, and panelist_id.
- Look up the CandidateStatus record using the candidate_id from CustomData.
- Based on CustomData.outcome_reason:

**"scheduled":**
- Update CandidateStatus.status to INTERVIEW_SCHEDULED.
- Using the CustomData.selected_slot (ISO 8601 string), parse it into a datetime object.
- Match Slot: Find the corresponding Panelist using the panelist_id from CustomData and their specific Slot object that matches this selected_slot's date and time. (It's crucial that the selected_slot format allows unambiguous matching to one of the originally offered slots).
- Create a new Interview record, linking it to the candidate and panelist(s), and storing the scheduled time.
- Update that matched Slot by setting is_booked: true, booked_candidate_id, and booked_interview_id.
- Advance Panelist: For the next scheduling attempt, the system will move to the next candidate and the next panelist in the array to ensure load balancing and fair distribution.

**"busy" or CallStatus/HangupCause indicating busy/no answer:**
- Increment CandidateStatus.interview_attempts.
- If CandidateStatus.interview_attempts < 3 (maximum 3 retries): Set CandidateStatus.status to RETRY_REQUIRED. Add a new scheduling_task to the Celery queue for the same candidate with a 30-minute delay.
- If CandidateStatus.interview_attempts == 3: Set CandidateStatus.status to MAX_RETRIES_EXCEEDED.
- Maintain Panelist: For the next scheduling attempt, the system will attempt to schedule the next candidate with the current panelist (if they have other available slots). This "sticky panelist" approach aims to fill a panelist's slots if they are available for multiple candidates.

**"not_interested":**
- Set CandidateStatus.status to NOT_INTERESTED.
- No further retries.
- Maintain Panelist: Move to the next candidate and current panelist.

**Other outcomes (e.g., "no_valid_response"):**
- Treat similar to "busy" (increment attempts, retry if limits allow, else MAX_RETRIES_EXCEEDED).

- Return HTTP 200 OK to Plivo to acknowledge receipt.

### 6.4. Load Balancing Logic (Simplified MVP)

- **Candidate Queue**: Candidates are processed from a queue.
- **Panelist Rotation**: The system will keep track of the "currently selected panelist". After an interview is successfully scheduled with this panelist, the system moves to the next panelist in a defined order (e.g., alphabetical by panelist_id). If an interview is not scheduled (retry, not interested), the system attempts the next candidate with the same panelist (if they still have available slots) to fill up that panelist's schedule first, before moving to the next panelist only if the current one has no more slots.

## 7. Non-Functional Requirements

- **Performance**: The system should be able to handle a moderate volume of concurrent call attempts. Celery for background processing helps manage load.
- **Scalability**: Initial deployment is local. Future considerations for scaling will involve Dockerization and orchestration (e.g., Kubernetes).
- **Reliability**: Automated retries for failed calls (up to 3 attempts). Idempotent webhook handling where possible.
- **Security**: Basic environment variable management. No external APIs beyond Plivo are exposed or consumed currently.
- **Observability**: Console logging for operational insights.
- **Maintainability**: Clean, modular Python code following FastAPI best practices. Pydantic for data validation.

## 8. Technical Design Details

### 8.1. FastAPI Endpoints

- **POST /api/v1/start-scheduling**: Receives the initial payload to seed the DB and trigger scheduling jobs.
  - Input: JSON payload as defined in Section 6.1.
  - Output: HTTP 202 Accepted, with a message indicating successful receipt and initiation of scheduling.

- **POST /plivo/status-callback**: Webhook endpoint for Plivo to send call status and extracted natural language information.
  - Input: Plivo-specific JSON payload (containing standard call details and CustomData with candidate_id, selected_slot, outcome_reason, etc.).
  - Output: HTTP 200 OK.

### 8.2. Celery Configuration

- **Broker**: Redis (local Docker container).
- **Backend**: Redis.
- **Task**: `schedule_interview_task` will be the main Celery task responsible for picking a candidate, selecting a panelist/slot, and initiating the Plivo call.

### 8.3. Environment Variables

- `MONGO_URI`: MongoDB connection string for the microservice's DB.
- `REDIS_HOST`: Redis host for Celery.
- `REDIS_PORT`: Redis port for Celery.
- `PLIVO_ACCOUNT_ID`: Your unique Plivo account identifier.
- `PLIVO_INTERVIEW_FLOW_ID`: The ID of your pre-configured Plivo Agent Flow for interview scheduling.
- `PLIVO_WEBHOOK_URL`: Base URL of the microservice's webhook endpoint for Plivo callbacks (e.g., http://your_local_ip:8000/plivo/status-callback).