# Claude Code Agent Instructions

## Project Setup Instructions

Create a complete Interview Scheduling Microservice (ISM) based on the attached PRD. Follow these exact steps:

### 1. Project Structure
```
interview-scheduling-microservice/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── candidate.py
│   │   ├── panelist.py
│   │   └── interview.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── scheduling.py
│   │   └── webhook.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── plivo_service.py
│   │   ├── scheduling_service.py
│   │   └── database_service.py
│   ├── tasks/
│   │   ├── __init__.py
│   │   └── celery_tasks.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py
│   │   └── database.py
│   └── utils/
│       ├── __init__.py
│       └── helpers.py
├── requirements.txt
├── docker-compose.yml
├── Dockerfile
├── .env.example
└── README.md
```

### 2. Technology Requirements
- **Framework**: FastAPI with Pydantic for data validation
- **Database**: MongoDB with Motor (async driver)
- **Task Queue**: Celery with Redis broker
- **HTTP Client**: httpx for async HTTP requests
- **Environment**: python-dotenv for environment variables

### 3. Core Implementation Requirements

#### Models (Pydantic)
- Implement all three MongoDB collections as defined in the PRD
- Use proper field validation and type hints
- Include both request/response models and database models

#### API Endpoints
- `POST /api/v1/start-scheduling` - Initialize system with candidates and panelists
- `POST /plivo/status-callback` - Handle Plivo webhook responses
- Include proper error handling and logging

#### Services
- **PlivoService**: Handle Agent Flow API calls with proper payload structure
- **SchedulingService**: Manage panelist rotation and load balancing logic
- **DatabaseService**: MongoDB operations with proper error handling

#### Celery Tasks
- `schedule_interview_task`: Main task for processing candidate calls
- Implement retry logic with 30-minute delays
- Proper error handling and status updates

#### Configuration
- Environment variable management
- MongoDB connection setup
- Redis configuration for Celery
- Plivo API configuration

### 4. Key Implementation Details

#### Status update for panelist, candidate and interview scheduled or not
- Handle Plivo webhook with CustomData containing:
  - `candidate_id`, `panelist_id`, `selected_slot`, `outcome_reason`
- Parse ISO 8601 datetime format for slot matching
- Update slot booking status atomically

#### Error Handling
- Comprehensive try-catch blocks
- Proper HTTP status codes
- Detailed logging for debugging

#### Database Operations
- Atomic updates for slot booking
- Proper indexing on candidate_id and panelist_id
- Connection pooling and error recovery

### 5. Docker Setup
- docker-compose.yml with MongoDB and Redis


### 6. Documentation
- Comprehensive README with setup instructions
- API documentation
- Environment variable explanations

## Single Powerful Prompt

Build a complete Interview Scheduling Microservice using Python/FastAPI based on the provided PRD. This system automates interview scheduling through Plivo's Agent Flow API with NLP-based slot selection.

**Core Requirements:**
1. **FastAPI Application** with async/await patterns throughout
2. **MongoDB Integration** using Motor driver with the three collections: CandidateStatus, Panelist, Interview
3. **Celery Task Queue** with Redis broker for background call processing
4. **Plivo Integration** using Agent Flow API with proper webhook handling
5. **Load Balancing Logic** implementing panelist rotation and "sticky panelist" approach
6. **Complete Docker Setup** with docker-compose for local development

**Key Features to Implement:**
- POST `/api/v1/start-scheduling` endpoint that seeds database and triggers Celery tasks
- POST `/plivo/status-callback` webhook handling NLP-extracted slot selection
- Sophisticated scheduling logic with 3-retry mechanism and 30-minute delays
- Atomic database operations for slot booking and status updates
- Comprehensive error handling and logging throughout
- Environment-based configuration management

**Technical Specifications:**
- Use Pydantic models for all data validation
- Implement proper async database operations
- Handle Plivo's CustomData format with candidate_id, selected_slot, outcome_reason
- Parse ISO 8601 datetime formats for slot matching
- Maintain interview attempt counters and status transitions
- Implement panelist rotation after successful bookings

**Project Structure:** Follow the exact folder structure provided above with proper separation of concerns between models, routes, services, and tasks.

**Docker Requirements:** Create production-ready Dockerfile and docker-compose.yml with MongoDB, Redis, and the FastAPI application properly networked.

Make this production-ready with proper error handling, logging, and documentation. Include a comprehensive README with setup instructions and API documentation.