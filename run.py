#!/usr/bin/env python3
"""
Startup script for Interview Scheduling Microservice.

This script starts the FastAPI application using uvicorn.
"""

import uvicorn
from app.core.config import get_settings

if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.environment == "development",
        log_level="info"
    )
